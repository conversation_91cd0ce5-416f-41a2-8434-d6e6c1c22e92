-- CreateTable
CREATE TABLE "plan_components" (
    "id" SERIAL NOT NULL,
    "plan_id" INTEGER NOT NULL,
    "component_id" INTEGER NOT NULL,
    "quantity" DECIMAL(10,2) NOT NULL,
    "unit_price" DECIMAL(12,2) NOT NULL,
    "total_price" DECIMAL(14,2) NOT NULL,
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "plan_components_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "plan_components_plan_id_component_id_key" ON "plan_components"("plan_id", "component_id");

-- AddForeignKey
ALTER TABLE "plan_components" ADD CONSTRAINT "plan_components_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "conveyor_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddF<PERSON>ignKey
ALTER TABLE "plan_components" ADD CONSTRAINT "plan_components_component_id_fkey" FOREIGN KEY ("component_id") REFERENCES "component_master"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
