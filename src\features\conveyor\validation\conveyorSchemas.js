import { z } from "zod";

/**
 * Conveyor Plan validation schema
 */
export const createConveyorPlanSchema = z.object({
  planName: z
    .string()
    .min(1, "Plan name is required")
    .min(2, "Plan name must be at least 2 characters")
    .max(150, "Plan name must be less than 150 characters"),
  conveyorType: z.string().min(1, "Conveyor type is required"),
  totalLengthM: z.coerce
    .number()
    .positive("Total length must be a positive number")
    .max(10000, "Total length cannot exceed 10,000 meters"),
  loadType: z.enum(["LIGHT", "MEDIUM", "HEAVY"], {
    required_error: "Load type is required",
    invalid_type_error: "Invalid load type selected",
  }),
  capacityTph: z
    .union([z.string(), z.number()])
    .optional()
    .transform((val) => {
      if (val === "" || val === null || val === undefined) return undefined;
      return typeof val === "string" ? parseFloat(val) : val;
    })
    .refine((val) => val === undefined || (val > 0 && val <= 10000), {
      message: "Capacity must be a positive number not exceeding 10,000 TPH",
    }),
  inclinationAngle: z
    .union([z.string(), z.number()])
    .optional()
    .transform((val) => {
      if (val === "" || val === null || val === undefined) return undefined;
      return typeof val === "string" ? parseFloat(val) : val;
    })
    .refine((val) => val === undefined || (val >= -90 && val <= 90), {
      message: "Inclination angle must be between -90 and 90 degrees",
    }),
  driveType: z
    .string()
    .max(100, "Drive type must be less than 100 characters")
    .optional(),
  environment: z
    .string()
    .max(255, "Environment description must be less than 255 characters")
    .optional(),
  region: z
    .string()
    .max(100, "Region must be less than 100 characters")
    .optional(),
  siteConditions: z
    .string()
    .max(1000, "Site conditions must be less than 1000 characters")
    .optional(),
  specialRequirements: z
    .string()
    .max(1000, "Special requirements must be less than 1000 characters")
    .optional(),
  status: z
    .enum(["Draft", "Submitted", "Approved", "Rejected"])
    .default("Draft"),
  components: z
    .array(
      z.object({
        componentId: z.number().positive("Component ID is required"),
        quantity: z
          .number()
          .positive("Quantity must be a positive number")
          .max(10000, "Quantity cannot exceed 10,000"),
        unitPrice: z
          .number()
          .positive("Unit price must be a positive number")
          .max(1000000, "Unit price cannot exceed 1,000,000"),
        totalPrice: z
          .number()
          .positive("Total price must be a positive number")
          .max(10000000, "Total price cannot exceed 10,000,000"),
        notes: z
          .string()
          .max(500, "Notes must be less than 500 characters")
          .optional(),
      })
    )
    .optional()
    .default([]),
});

export const updateConveyorPlanSchema = createConveyorPlanSchema.partial();

/**
 * Conveyor Plan search/filter validation schema
 */
export const conveyorPlanFilterSchema = z.object({
  search: z.string().optional(),
  conveyorType: z.string().optional(),
  loadType: z.enum(["LIGHT", "MEDIUM", "HEAVY"]).optional(),
  status: z.enum(["Draft", "Submitted", "Approved", "Rejected"]).optional(),
  region: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z
    .enum([
      "planName",
      "conveyorType",
      "totalLengthM",
      "loadType",
      "status",
      "createdAt",
    ])
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

/**
 * Conveyor types
 */
export const conveyorTypes = [
  "Belt Conveyor",
  "Roller Conveyor",
  "Screw Conveyor",
  "Chain Conveyor",
  "Pneumatic Conveyor",
  "Bucket Elevator",
  "Vibrating Conveyor",
  "Magnetic Conveyor",
];

/**
 * Load types
 */
export const loadTypes = [
  {
    value: "LIGHT",
    label: "Light (< 50 kg/m)",
    description: "Small packages, light materials",
  },
  {
    value: "MEDIUM",
    label: "Medium (50-200 kg/m)",
    description: "Standard industrial loads",
  },
  {
    value: "HEAVY",
    label: "Heavy (> 200 kg/m)",
    description: "Heavy machinery, bulk materials",
  },
];

/**
 * Drive types
 */
export const driveTypes = [
  "Motorized - AC Motor",
  "Motorized - DC Motor",
  "Motorized - Servo Motor",
  "Manual",
  "Gravity Fed",
  "Pneumatic Drive",
  "Hydraulic Drive",
];

/**
 * Environment types
 */
export const environmentTypes = [
  "Indoor - Clean",
  "Indoor - Dusty",
  "Indoor - Humid",
  "Outdoor - Normal",
  "Outdoor - Harsh Weather",
  "Corrosive Environment",
  "High Temperature",
  "Low Temperature",
  "Food Grade",
  "Pharmaceutical Grade",
];

/**
 * Regions
 */
export const regions = [
  "North India",
  "South India",
  "East India",
  "West India",
  "Central India",
  "Northeast India",
  "International",
];
