// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String     @id @default(cuid())
  email     String     @unique
  password  String
  firstName String?    @map("first_name")
  lastName  String?    @map("last_name")
  role      UserRole   @default(USER)
  status    UserStatus @default(ACTIVE)
  avatar    String?

  // Timestamps
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  lastLoginAt DateTime? @map("last_login_at")

  // Relations
  sessions  Session[]
  invitedBy String?   @map("invited_by")
  inviter   User?     @relation("UserInvitations", fields: [invitedBy], references: [id])
  invitees  User[]    @relation("UserInvitations")

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Device/browser info
  userAgent String? @map("user_agent")
  ipAddress String? @map("ip_address")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model AuditLog {
  id         String   @id @default(cuid())
  userId     String?  @map("user_id")
  action     String
  resource   String
  resourceId String?  @map("resource_id")
  details    Json?
  ipAddress  String?  @map("ip_address")
  userAgent  String?  @map("user_agent")
  createdAt  DateTime @default(now()) @map("created_at")

  @@map("audit_logs")
}

enum UserRole {
  ADMIN
  MANAGER
  USER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

// Conveyor System Models

model EquipmentCategory {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  status      Boolean  @default(true)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  equipment EquipmentMaster[]

  @@map("equipment_categories")
}

model EquipmentMaster {
  id             Int       @id @default(autoincrement())
  name           String
  code           String    @unique
  categoryId     Int       @map("category_id")
  subCategory    String?   @map("sub_category")
  description    String?
  specifications Json?
  uom            String
  baseRate       Decimal   @map("base_rate") @db.Decimal(12, 2)
  regionRates    Json?     @map("region_rates")
  effectiveFrom  DateTime  @map("effective_from")
  validUpto      DateTime? @map("valid_upto")
  costBreakup    Json?     @map("cost_breakup")
  status         Boolean   @default(true)
  createdBy      String?   @map("created_by")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedBy      String?   @map("updated_by")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  // Relations
  category   EquipmentCategory @relation(fields: [categoryId], references: [id])
  components ComponentMaster[]

  @@map("equipment_master")
}

model ComponentMaster {
  id                Int       @id @default(autoincrement())
  name              String
  code              String    @unique
  category          String
  equipmentId       Int       @map("equipment_id")
  description       String?
  uom               String
  baseRate          Decimal   @map("base_rate") @db.Decimal(12, 2)
  rateEffectiveFrom DateTime  @map("rate_effective_from")
  validUpto         DateTime? @map("valid_upto")
  vendorInfo        String?   @map("vendor_info")
  status            Boolean   @default(true)
  createdBy         String?   @map("created_by")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedBy         String?   @map("updated_by")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // Relations
  equipment      EquipmentMaster @relation(fields: [equipmentId], references: [id])
  planComponents PlanComponent[]

  @@map("component_master")
}

model ConveyorPlan {
  id                  Int      @id @default(autoincrement())
  planName            String   @map("plan_name")
  conveyorType        String   @map("conveyor_type")
  totalLengthM        Decimal  @map("total_length_m") @db.Decimal(10, 2)
  loadType            String   @map("load_type")
  capacityTph         Decimal? @map("capacity_tph") @db.Decimal(10, 2)
  inclinationAngle    Decimal? @map("inclination_angle") @db.Decimal(5, 2)
  driveType           String?  @map("drive_type")
  environment         String?
  region              String?
  siteConditions      String?  @map("site_conditions")
  specialRequirements String?  @map("special_requirements")
  status              String   @default("Draft")
  createdBy           String?  @map("created_by")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedBy           String?  @map("updated_by")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  estimationSheets EstimationSheet[]
  planComponents   PlanComponent[]

  @@map("conveyor_plans")
}

model PlanComponent {
  id          Int      @id @default(autoincrement())
  planId      Int      @map("plan_id")
  componentId Int      @map("component_id")
  quantity    Decimal  @db.Decimal(10, 2)
  unitPrice   Decimal  @map("unit_price") @db.Decimal(12, 2)
  totalPrice  Decimal  @map("total_price") @db.Decimal(14, 2)
  notes       String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  plan      ConveyorPlan    @relation(fields: [planId], references: [id], onDelete: Cascade)
  component ComponentMaster @relation(fields: [componentId], references: [id])

  @@unique([planId, componentId])
  @@map("plan_components")
}

model EstimationSheet {
  id                Int      @id @default(autoincrement())
  planId            Int      @map("plan_id")
  estimationVersion Int      @default(1) @map("estimation_version")
  equipmentCost     Decimal? @map("equipment_cost") @db.Decimal(14, 2)
  componentCost     Decimal? @map("component_cost") @db.Decimal(14, 2)
  laborCost         Decimal? @map("labor_cost") @db.Decimal(14, 2)
  overheadCost      Decimal? @map("overhead_cost") @db.Decimal(14, 2)
  discountPercent   Decimal? @map("discount_percent") @db.Decimal(5, 2)
  markupPercent     Decimal? @map("markup_percent") @db.Decimal(5, 2)
  totalCost         Decimal? @map("total_cost") @db.Decimal(14, 2)
  notes             String?
  status            String   @default("Draft")
  generatedBy       String?  @map("generated_by")
  generatedAt       DateTime @default(now()) @map("generated_at")

  // Relations
  plan ConveyorPlan @relation(fields: [planId], references: [id])

  @@map("estimation_sheets")
}

// Enums for Conveyor System
enum ConveyorType {
  BELT
  ROLLER
  SCREW
  CHAIN
  PNEUMATIC
}

enum LoadType {
  LIGHT
  MEDIUM
  HEAVY
}

enum DriveType {
  MOTORIZED
  MANUAL
  GRAVITY
}

enum PlanStatus {
  DRAFT
  SUBMITTED
  APPROVED
  REJECTED
}

enum EstimationStatus {
  DRAFT
  FINAL
  SENT
  APPROVED
}
