"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Search, Plus, Package2 } from "lucide-react";
import { useApi } from "@/hooks/useApi";
import { useToast } from "@/hooks/useToast";

export default function ComponentSelectionDialog({
  isOpen,
  onClose,
  onComponentSelect,
  selectedComponents = [],
}) {
  const [components, setComponents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [quantity, setQuantity] = useState("");
  const [notes, setNotes] = useState("");

  const { apiCall } = useApi();
  const { showToast } = useToast();

  // Fetch components
  const fetchComponents = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchTerm) params.append("search", searchTerm);
      params.append("status", "true"); // Only active components
      params.append("limit", "50"); // Get more components for selection

      console.log("🔍 Fetching components with params:", params.toString());
      const response = await apiCall(`/api/components?${params}`);
      console.log("📡 Components API response:", response);

      if (response.success) {
        console.log("✅ Components data:", response.data);
        setComponents(response.data || []);
      } else {
        console.log("❌ Components API failed:", response);
        showToast(response.message || "Failed to fetch components", "error");
      }
    } catch (error) {
      console.error("💥 Components fetch error:", error);
      showToast("Failed to fetch components", "error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchComponents();
    }
  }, [isOpen, searchTerm]);

  const handleComponentClick = (component) => {
    // Check if component is already selected
    const isAlreadySelected = selectedComponents.some(
      (sc) => sc.componentId === component.id
    );
    if (isAlreadySelected) {
      showToast("Component already selected", "warning");
      return;
    }

    setSelectedComponent(component);
    setQuantity("");
    setNotes("");
  };

  const handleAddComponent = () => {
    if (!selectedComponent || !quantity || parseFloat(quantity) <= 0) {
      showToast(
        "Please select a component and enter a valid quantity",
        "error"
      );
      return;
    }

    const qty = parseFloat(quantity);

    // Validate quantity limits
    if (qty > 10000) {
      showToast("Quantity cannot exceed 10,000", "error");
      return;
    }

    const unitPrice = parseFloat(selectedComponent.baseRate);

    // Validate unit price
    if (!unitPrice || unitPrice <= 0) {
      showToast("Invalid component price", "error");
      return;
    }

    const totalPrice = qty * unitPrice;

    const planComponent = {
      componentId: selectedComponent.id,
      component: selectedComponent,
      quantity: qty,
      unitPrice: unitPrice,
      totalPrice: totalPrice,
      notes: notes.trim() || null,
    };

    onComponentSelect(planComponent);
    setSelectedComponent(null);
    setQuantity("");
    setNotes("");
    showToast("Component added successfully", "success");
  };

  const handleCancel = () => {
    setSelectedComponent(null);
    setQuantity("");
    setNotes("");
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Package2 className="w-5 h-5 mr-2" />
            Select Components
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
          {/* Component List */}
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search components..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="border rounded-lg overflow-hidden max-h-96 overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Component</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>UOM</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-4">
                        Loading components...
                      </TableCell>
                    </TableRow>
                  ) : components.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={3}
                        className="text-center py-4 text-gray-500"
                      >
                        No components found
                      </TableCell>
                    </TableRow>
                  ) : (
                    components.map((component) => {
                      const isSelected = selectedComponents.some(
                        (sc) => sc.componentId === component.id
                      );
                      const isCurrentlySelected =
                        selectedComponent?.id === component.id;

                      return (
                        <TableRow
                          key={component.id}
                          className={`cursor-pointer hover:bg-gray-50 ${
                            isCurrentlySelected
                              ? "bg-blue-50 border-blue-200"
                              : ""
                          } ${isSelected ? "opacity-50" : ""}`}
                          onClick={() =>
                            !isSelected && handleComponentClick(component)
                          }
                        >
                          <TableCell>
                            <div>
                              <p className="font-medium">{component.name}</p>
                              <p className="text-sm text-gray-500">
                                {component.code}
                              </p>
                              {isSelected && (
                                <Badge variant="secondary" className="mt-1">
                                  Already Selected
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {formatCurrency(component.baseRate)}
                          </TableCell>
                          <TableCell>{component.uom}</TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Component Details & Quantity */}
          <div className="space-y-4">
            {selectedComponent ? (
              <Card className="card-vibrant">
                <CardHeader>
                  <CardTitle className="text-lg">Add Component</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">
                      Selected Component
                    </Label>
                    <p className="text-lg font-semibold">
                      {selectedComponent.name}
                    </p>
                    <p className="text-sm text-gray-600">
                      {selectedComponent.code}
                    </p>
                    <p className="text-sm text-gray-600">
                      {selectedComponent.description}
                    </p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Unit Price
                      </Label>
                      <p className="text-lg font-semibold text-green-600">
                        {formatCurrency(selectedComponent.baseRate)}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        UOM
                      </Label>
                      <p className="text-lg">{selectedComponent.uom}</p>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="quantity">Quantity *</Label>
                    <Input
                      id="quantity"
                      type="number"
                      step="0.01"
                      min="0.01"
                      value={quantity}
                      onChange={(e) => setQuantity(e.target.value)}
                      placeholder="Enter quantity"
                    />
                  </div>

                  {quantity && parseFloat(quantity) > 0 && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Total Price
                      </Label>
                      <p className="text-xl font-bold text-blue-600">
                        {formatCurrency(
                          parseFloat(quantity) *
                            parseFloat(selectedComponent.baseRate)
                        )}
                      </p>
                    </div>
                  )}

                  <div>
                    <Label htmlFor="notes">Notes (Optional)</Label>
                    <Input
                      id="notes"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Additional notes..."
                    />
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      onClick={handleAddComponent}
                      className="flex-1"
                      disabled={!quantity || parseFloat(quantity) <= 0}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Component
                    </Button>
                    <Button variant="outline" onClick={handleCancel}>
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="card-vibrant">
                <CardContent className="p-6 text-center">
                  <Package2 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">
                    Select a component from the list to add it to your plan
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
