"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Plus, Package2, Edit, Trash2, MoreHorizontal } from "lucide-react";
import ComponentSelectionDialog from "./ComponentSelectionDialog";

export default function PlanComponentsManager({
  components = [],
  onComponentsChange,
  disabled = false,
}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingComponent, setEditingComponent] = useState(null);

  const handleAddComponent = (newComponent) => {
    const updatedComponents = [...components, newComponent];
    onComponentsChange(updatedComponents);
  };

  const handleEditComponent = (index, field, value) => {
    const updatedComponents = [...components];
    const component = { ...updatedComponents[index] };

    if (field === "quantity") {
      const qty = parseFloat(value) || 0;
      if (qty < 0) {
        // Don't allow negative quantities
        return;
      }
      component.quantity = qty;
      component.totalPrice = qty * component.unitPrice;
    } else {
      component[field] = value;
    }

    updatedComponents[index] = component;
    onComponentsChange(updatedComponents);
  };

  const handleRemoveComponent = (index) => {
    if (confirm("Are you sure you want to remove this component?")) {
      const updatedComponents = components.filter((_, i) => i !== index);
      onComponentsChange(updatedComponents);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  const getTotalCost = () => {
    return components.reduce(
      (total, comp) => total + (parseFloat(comp.totalPrice) || 0),
      0
    );
  };

  return (
    <Card className="card-vibrant">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center">
            <Package2 className="w-5 h-5 mr-2" />
            Plan Components ({components.length})
          </CardTitle>
          {!disabled && (
            <Button
              type="button"
              onClick={() => setIsDialogOpen(true)}
              variant="outline"
              size="sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Component
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {components.length === 0 ? (
          <div className="text-center py-8">
            <Package2 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600 mb-4">No components added yet</p>
            {!disabled && (
              <Button
                type="button"
                onClick={() => setIsDialogOpen(true)}
                variant="outline"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add First Component
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Component</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>UOM</TableHead>
                    <TableHead>Total Price</TableHead>
                    <TableHead>Notes</TableHead>
                    {!disabled && <TableHead>Actions</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {components.map((comp, index) => (
                    <TableRow key={`${comp.componentId}-${index}`}>
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {comp.component?.name || "Unknown Component"}
                          </p>
                          <p className="text-sm text-gray-500">
                            {comp.component?.code}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>{formatCurrency(comp.unitPrice)}</TableCell>
                      <TableCell>
                        {disabled ? (
                          <span>{comp.quantity}</span>
                        ) : (
                          <Input
                            type="number"
                            step="0.01"
                            min="0.01"
                            value={comp.quantity}
                            onChange={(e) =>
                              handleEditComponent(
                                index,
                                "quantity",
                                e.target.value
                              )
                            }
                            className="w-20"
                          />
                        )}
                      </TableCell>
                      <TableCell>{comp.component?.uom}</TableCell>
                      <TableCell className="font-semibold text-green-600">
                        {formatCurrency(comp.totalPrice)}
                      </TableCell>
                      <TableCell>
                        {disabled ? (
                          <span className="text-sm">{comp.notes || "-"}</span>
                        ) : (
                          <Input
                            value={comp.notes || ""}
                            onChange={(e) =>
                              handleEditComponent(
                                index,
                                "notes",
                                e.target.value
                              )
                            }
                            placeholder="Notes..."
                            className="w-32"
                          />
                        )}
                      </TableCell>
                      {!disabled && (
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleRemoveComponent(index)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Remove
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Total Cost Summary */}
            <div className="border-t pt-4">
              <div className="flex justify-between items-center">
                <span className="text-lg font-medium">
                  Total Component Cost:
                </span>
                <span className="text-xl font-bold text-blue-600">
                  {formatCurrency(getTotalCost())}
                </span>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      {/* Component Selection Dialog */}
      <ComponentSelectionDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onComponentSelect={handleAddComponent}
        selectedComponents={components}
      />
    </Card>
  );
}
